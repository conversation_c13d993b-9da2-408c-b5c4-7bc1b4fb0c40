# GSConnect Mount Manager Configuration
# This file contains configuration options for the mount manager

# Polling interval in seconds (how often to check for mounts)
POLL_INTERVAL=5

# Root directory where GVFS mounts are located
MOUNT_ROOT="/run/user/$(id -u)/gvfs"

# Directory where configuration and state files are stored
CONFIG_DIR="$HOME/.config/gsconnect-mount-manager"

# Location of GTK bookmarks file
BOOKMARK_FILE="$HOME/.config/gtk-3.0/bookmarks"

# Mount structure directory (where device folders are created)
MOUNT_STRUCTURE_DIR="$HOME/.gsconnect-mount"

# Custom symlink directory (default: same as MOUNT_STRUCTURE_DIR)
# Set to empty string to use MOUNT_STRUCTURE_DIR
SYMLINK_DIR=""

# Custom symlink prefix (added before device name)
# Example: "Phone-" would create "Phone-DeviceName" symlinks
SYMLINK_PREFIX=""

# Custom symlink suffix (added after device name)  
# Example: "-Storage" would create "DeviceName-Storage" symlinks
SYMLINK_SUFFIX=""

# Enable desktop notifications (true/false)
ENABLE_NOTIFICATIONS=true

# Log level (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# Maximum log file size in MB (0 = no limit)
MAX_LOG_SIZE=10

# Number of log files to keep in rotation
LOG_ROTATE_COUNT=5

# Storage Configuration
# Enable internal storage (storage/emulated/0) - Android internal storage
ENABLE_INTERNAL_STORAGE=true

# Enable external storage detection (SD cards, USB OTG) - storage/[UUID] or storage/sdcard1
ENABLE_EXTERNAL_STORAGE=true

# Custom internal storage path (default: storage/emulated/0)
INTERNAL_STORAGE_PATH="storage/emulated/0"

# Internal storage folder name within device directory
INTERNAL_STORAGE_NAME="Internal"

# External storage folder name within device directory
EXTERNAL_STORAGE_NAME="SDCard"

# USB OTG storage folder name within device directory
USB_STORAGE_NAME="USB-OTG"

# External storage detection patterns (space-separated list)
# Patterns to match external storage directories
EXTERNAL_STORAGE_PATTERNS="storage/[0-9A-F][0-9A-F][0-9A-F][0-9A-F]* storage/sdcard1 storage/extSdCard storage/external_sd storage/usbotg"

# Maximum number of external storage devices to detect
MAX_EXTERNAL_STORAGE=3

# Enable automatic cleanup of broken symlinks (true/false)
AUTO_CLEANUP=true

# Timeout in seconds to wait for device storage to become available
STORAGE_TIMEOUT=30

# Enable verbose logging (true/false)
VERBOSE=false
